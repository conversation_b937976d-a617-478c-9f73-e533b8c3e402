# Document de Test - Application GesGar v1.0

## Table des Matières
1. [Vue d'ensemble](#vue-densemble)
2. [Environnement de test](#environnement-de-test)
3. [Rôles et permissions](#rôles-et-permissions)
4. [Tests d'authentification](#tests-dauthentification)
5. [Tests des modules principaux](#tests-des-modules-principaux)
6. [Tests d'API](#tests-dapi)
7. [Tests d'interface utilisateur](#tests-dinterface-utilisateur)
8. [Tests de sécurité](#tests-de-sécurité)
9. [Tests de performance](#tests-de-performance)
10. [Scénarios de test complets](#scénarios-de-test-complets)

## Vue d'ensemble

L'application GesGar est un système de gestion des garanties financières développé avec Next.js, React, TypeScript et Prisma. Elle permet la gestion complète du cycle de vie des garanties, des allocations, des mainlevées et des mises en jeu.

### Architecture technique
- **Frontend**: Next.js 14 avec React et TypeScript
- **Backend**: API Routes Next.js
- **Base de données**: PostgreSQL avec Prisma ORM
- **Authentification**: NextAuth.js
- **UI**: Tailwind CSS + shadcn/ui
- **Validation**: Zod schemas

## Environnement de test

### Prérequis
- Node.js 18+
- PostgreSQL
- Variables d'environnement configurées
- Base de données de test séparée

### Configuration
```bash
# Installation des dépendances
npm install

# Configuration de la base de données de test
cp .env.example .env.test
# Modifier DATABASE_URL pour pointer vers la DB de test

# Migration de la base de données
npx prisma migrate deploy

# Génération du client Prisma
npx prisma generate

# Lancement en mode développement
npm run dev
```

### Données de test
Créer des utilisateurs de test pour chaque rôle :
- **Administrateur**: <EMAIL> / password123
- **GestionnaireGesGar**: <EMAIL> / password123
- **AnalysteFinancier**: <EMAIL> / password123
- **Partenaire**: <EMAIL> / password123

## Rôles et permissions

### Administrateur
- Accès complet à toutes les fonctionnalités
- Gestion des utilisateurs
- Configuration système
- Toutes les opérations CRUD

### GestionnaireGesGar
- Gestion des garanties
- Traitement des mainlevées et mises en jeu
- Gestion des allocations
- Configuration des entités métier

### AnalysteFinancier
- Consultation des données
- Génération de rapports
- Analyse des KPIs

### Partenaire
- Accès filtré à ses propres données
- Demandes de mainlevées et mises en jeu
- Consultation de ses garanties et allocations

## Tests d'authentification

### Test 1: Connexion réussie
**Objectif**: Vérifier la connexion avec des identifiants valides
**Étapes**:
1. Accéder à `/auth/connexion`
2. Saisir email et mot de passe valides
3. Cliquer sur "Se connecter"
**Résultat attendu**: Redirection vers le dashboard

### Test 2: Connexion échouée
**Objectif**: Vérifier le comportement avec des identifiants invalides
**Étapes**:
1. Accéder à `/auth/connexion`
2. Saisir des identifiants incorrects
3. Cliquer sur "Se connecter"
**Résultat attendu**: Message d'erreur affiché

### Test 3: Déconnexion
**Objectif**: Vérifier la déconnexion
**Étapes**:
1. Se connecter
2. Cliquer sur le menu utilisateur
3. Sélectionner "Déconnexion"
**Résultat attendu**: Redirection vers la page de connexion

### Test 4: Protection des routes
**Objectif**: Vérifier que les routes protégées redirigent vers la connexion
**Étapes**:
1. Accéder directement à `/dashboard` sans être connecté
**Résultat attendu**: Redirection vers `/auth/connexion`

### Test 5: Inscription (si activée)
**Objectif**: Vérifier le processus d'inscription
**Étapes**:
1. Accéder à `/auth/inscription`
2. Remplir le formulaire avec des données valides
3. Soumettre
**Résultat attendu**: Compte créé et redirection vers la connexion

## Tests des modules principaux

### Module Configuration

#### Test 6: Gestion des Bailleurs
**Objectif**: CRUD complet des bailleurs
**Prérequis**: Connecté en tant qu'Administrateur ou GestionnaireGesGar

**Test 6.1: Création d'un bailleur**
1. Accéder à `/configuration/bailleurs`
2. Cliquer sur "Ajouter un Bailleur"
3. Remplir le formulaire avec des données valides
4. Soumettre
**Résultat attendu**: Bailleur créé et affiché dans la liste

**Test 6.2: Modification d'un bailleur**
1. Dans la liste des bailleurs, cliquer sur "Modifier"
2. Modifier les informations
3. Sauvegarder
**Résultat attendu**: Modifications enregistrées

**Test 6.3: Suppression d'un bailleur**
1. Cliquer sur "Supprimer" pour un bailleur
2. Confirmer la suppression
**Résultat attendu**: Bailleur supprimé (ou erreur si utilisé)

#### Test 7: Gestion des Partenaires
**Objectif**: CRUD complet des partenaires financiers

**Test 7.1: Création d'un partenaire**
1. Accéder à `/configuration/partenaires`
2. Cliquer sur "Ajouter un Partenaire"
3. Remplir tous les champs obligatoires
4. Soumettre
**Résultat attendu**: Partenaire créé

**Test 7.2: Validation des champs**
1. Essayer de créer un partenaire sans remplir les champs obligatoires
**Résultat attendu**: Messages d'erreur de validation

#### Test 8: Gestion des Secteurs d'Activité
**Objectif**: CRUD des secteurs d'activité

**Test 8.1: Création avec nom unique**
1. Créer un secteur avec un nom unique
**Résultat attendu**: Création réussie

**Test 8.2: Tentative de doublon**
1. Créer un secteur avec un nom existant
**Résultat attendu**: Erreur de contrainte d'unicité

#### Test 9: Gestion des Clients Bénéficiaires
**Objectif**: CRUD des clients avec validation conditionnelle

**Test 9.1: Création personne physique**
1. Créer un client de type "PersonnePhysique"
2. Vérifier que âge et genre sont requis
**Résultat attendu**: Validation correcte

**Test 9.2: Création personne morale**
1. Créer un client de type "PersonneMorale"
2. Vérifier que âge et genre ne sont pas requis
**Résultat attendu**: Création réussie sans âge/genre

#### Test 10: Gestion des Projets
**Objectif**: CRUD des projets avec relations

**Test 10.1: Création avec relations valides**
1. Créer un projet en sélectionnant secteur et client existants
**Résultat attendu**: Projet créé avec relations

### Module Lignes de Garantie

#### Test 11: Gestion des Lignes de Garantie
**Objectif**: CRUD des lignes de garantie

**Test 11.1: Création d'une ligne**
1. Accéder à `/lignes-garantie`
2. Créer une nouvelle ligne avec montant valide
**Résultat attendu**: Ligne créée avec montant disponible calculé

**Test 11.2: Validation du montant**
1. Essayer de créer une ligne avec montant négatif
**Résultat attendu**: Erreur de validation

### Module Allocations

#### Test 12: Gestion des Allocations
**Objectif**: CRUD des allocations ligne-partenaire

**Test 12.1: Création d'allocation**
1. Créer une allocation en sélectionnant ligne et partenaire
2. Vérifier que le montant ne dépasse pas le disponible
**Résultat attendu**: Allocation créée si montant valide

**Test 12.2: Dépassement de montant**
1. Essayer d'allouer plus que le montant disponible
**Résultat attendu**: Erreur de validation métier

### Module Garanties

#### Test 13: Gestion des Garanties
**Objectif**: Cycle de vie complet des garanties

**Test 13.1: Création d'une garantie**
1. Accéder à `/garanties`
2. Créer une garantie avec allocation et projet valides
**Résultat attendu**: Garantie créée avec statut initial

**Test 13.2: Modification du statut**
1. Modifier le statut d'une garantie existante
**Résultat attendu**: Statut mis à jour avec audit

**Test 13.3: Consultation des détails**
1. Cliquer sur une garantie pour voir les détails
**Résultat attendu**: Page de détail avec toutes les informations

### Module Mainlevées

#### Test 14: Processus de Mainlevée
**Objectif**: Cycle complet de demande et traitement

**Test 14.1: Demande de mainlevée (Partenaire)**
1. Se connecter en tant que Partenaire
2. Accéder à une garantie
3. Demander une mainlevée
**Résultat attendu**: Demande créée avec statut "EnAttente"

**Test 14.2: Traitement de mainlevée (Gestionnaire)**
1. Se connecter en tant que GestionnaireGesGar
2. Accéder aux mainlevées en attente
3. Approuver ou refuser une demande
**Résultat attendu**: Statut mis à jour, garantie modifiée si approuvée

### Module Mises en Jeu

#### Test 15: Processus de Mise en Jeu
**Objectif**: Cycle complet de demande et décision

**Test 15.1: Demande de mise en jeu**
1. Créer une demande de mise en jeu sur une garantie
**Résultat attendu**: Demande créée avec statut initial

**Test 15.2: Décision sur mise en jeu**
1. Traiter une demande de mise en jeu
2. Approuver avec montant
**Résultat attendu**: Garantie mise à jour, montant déduit

## Tests d'API

### Test 16: Endpoints d'authentification
**Objectif**: Vérifier les API d'auth

**Test 16.1: POST /api/auth/register**
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "nomUtilisateur": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "nom": "Test",
    "prenom": "User"
  }'
```
**Résultat attendu**: 201 Created avec données utilisateur

**Test 16.2: POST /api/auth/[...nextauth]**
Test de connexion via l'API NextAuth

### Test 17: Endpoints de configuration
**Objectif**: Vérifier les CRUD APIs

**Test 17.1: GET /api/configuration/bailleurs**
```bash
curl -H "Authorization: Bearer <token>" \
  http://localhost:3000/api/configuration/bailleurs
```
**Résultat attendu**: 200 OK avec liste des bailleurs

**Test 17.2: POST /api/configuration/bailleurs**
```bash
curl -X POST http://localhost:3000/api/configuration/bailleurs \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "nom": "Nouveau Bailleur",
    "description": "Description test"
  }'
```
**Résultat attendu**: 201 Created

### Test 18: Endpoints de garanties
**Objectif**: Vérifier les APIs métier

**Test 18.1: GET /api/garanties**
Vérifier la liste avec filtrage par rôle

**Test 18.2: GET /api/garanties/[id]**
Vérifier les détails d'une garantie

**Test 18.3: POST /api/garanties**
Créer une nouvelle garantie

### Test 19: Endpoints de mainlevées
**Objectif**: Vérifier le processus transactionnel

**Test 19.1: POST /api/garanties/[id]/mainlevees**
Créer une demande de mainlevée

**Test 19.2: PUT /api/mainlevees/[id]**
Traiter une mainlevée (transaction atomique)

### Test 20: Contrôles d'autorisation API
**Objectif**: Vérifier la sécurité des endpoints

**Test 20.1: Accès sans authentification**
```bash
curl http://localhost:3000/api/garanties
```
**Résultat attendu**: 401 Unauthorized

**Test 20.2: Accès avec rôle insuffisant**
Tenter d'accéder à un endpoint admin avec un compte Partenaire
**Résultat attendu**: 403 Forbidden

**Test 20.3: Filtrage des données par rôle**
Vérifier qu'un Partenaire ne voit que ses données

## Tests d'interface utilisateur

### Test 21: Navigation et Layout
**Objectif**: Vérifier l'interface générale

**Test 21.1: Sidebar selon le rôle**
1. Se connecter avec différents rôles
2. Vérifier que la sidebar affiche les bonnes options
**Résultat attendu**: Menus adaptés au rôle

**Test 21.2: Navbar responsive**
1. Tester sur différentes tailles d'écran
**Résultat attendu**: Interface adaptative

**Test 21.3: Recherche globale**
1. Utiliser la barre de recherche
**Résultat attendu**: Résultats pertinents

### Test 22: Formulaires
**Objectif**: Vérifier les interactions utilisateur

**Test 22.1: Validation côté client**
1. Soumettre un formulaire avec des erreurs
**Résultat attendu**: Messages d'erreur en temps réel

**Test 22.2: Sauvegarde et annulation**
1. Remplir un formulaire et annuler
**Résultat attendu**: Retour à l'état initial

### Test 23: Tableaux et pagination
**Objectif**: Vérifier l'affichage des données

**Test 23.1: Tri des colonnes**
1. Cliquer sur les en-têtes de colonnes
**Résultat attendu**: Tri appliqué

**Test 23.2: Pagination**
1. Naviguer entre les pages
**Résultat attendu**: Données chargées correctement

### Test 24: Notifications et feedback
**Objectif**: Vérifier les retours utilisateur

**Test 24.1: Messages de succès**
1. Effectuer une action réussie
**Résultat attendu**: Toast de confirmation

**Test 24.2: Messages d'erreur**
1. Provoquer une erreur
**Résultat attendu**: Message d'erreur clair

## Tests de sécurité

### Test 25: Authentification et sessions
**Objectif**: Vérifier la sécurité d'accès

**Test 25.1: Expiration de session**
1. Laisser la session expirer
2. Tenter une action
**Résultat attendu**: Redirection vers la connexion

**Test 25.2: Tentative de force brute**
1. Essayer plusieurs connexions échouées
**Résultat attendu**: Limitation ou blocage

### Test 26: Autorisation et contrôle d'accès
**Objectif**: Vérifier les permissions

**Test 26.1: Escalade de privilèges**
1. Tenter d'accéder à des fonctions non autorisées
**Résultat attendu**: Accès refusé

**Test 26.2: Manipulation d'URL**
1. Modifier les IDs dans les URLs
**Résultat attendu**: Vérification des permissions

### Test 27: Validation et injection
**Objectif**: Vérifier la sécurité des données

**Test 27.1: Injection SQL**
1. Tenter d'injecter du SQL dans les formulaires
**Résultat attendu**: Requêtes bloquées (Prisma protège)

**Test 27.2: XSS**
1. Injecter du JavaScript dans les champs
**Résultat attendu**: Contenu échappé

### Test 28: Audit et traçabilité
**Objectif**: Vérifier l'audit des actions

**Test 28.1: Enregistrement des actions**
1. Effectuer des modifications
2. Vérifier les logs d'audit
**Résultat attendu**: Actions tracées avec utilisateur et timestamp

## Tests de performance

### Test 29: Temps de réponse
**Objectif**: Vérifier les performances

**Test 29.1: Chargement des pages**
1. Mesurer le temps de chargement des pages principales
**Résultat attendu**: < 2 secondes

**Test 29.2: Requêtes API**
1. Mesurer le temps de réponse des APIs
**Résultat attendu**: < 500ms pour les requêtes simples

### Test 30: Charge et concurrence
**Objectif**: Tester sous charge

**Test 30.1: Utilisateurs simultanés**
1. Simuler plusieurs utilisateurs connectés
**Résultat attendu**: Performance maintenue

**Test 30.2: Gros volumes de données**
1. Tester avec de nombreux enregistrements
**Résultat attendu**: Pagination efficace

## Scénarios de test complets

### Scénario 1: Cycle complet de garantie
**Objectif**: Tester le processus métier de bout en bout

**Étapes**:
1. **Configuration initiale** (Administrateur)
   - Créer un bailleur
   - Créer un partenaire
   - Créer un secteur d'activité
   - Créer un client bénéficiaire
   - Créer un projet

2. **Gestion des lignes** (GestionnaireGesGar)
   - Créer une ligne de garantie
   - Vérifier le montant disponible

3. **Allocation** (GestionnaireGesGar)
   - Créer une allocation ligne-partenaire
   - Vérifier la déduction du montant disponible

4. **Octroi de garantie** (GestionnaireGesGar)
   - Créer une garantie sur l'allocation
   - Vérifier le statut initial

5. **Demande de mainlevée** (Partenaire)
   - Se connecter en tant que partenaire
   - Demander une mainlevée partielle

6. **Traitement de mainlevée** (GestionnaireGesGar)
   - Approuver la mainlevée
   - Vérifier la mise à jour transactionnelle

**Résultat attendu**: Processus complet sans erreur, données cohérentes

### Scénario 2: Gestion des erreurs et rollback
**Objectif**: Vérifier la robustesse du système

**Étapes**:
1. Créer une garantie
2. Demander une mainlevée
3. Simuler une erreur pendant le traitement
4. Vérifier que les données restent cohérentes

**Résultat attendu**: Rollback correct, pas de corruption de données

### Scénario 3: Workflow multi-utilisateurs
**Objectif**: Tester la collaboration

**Étapes**:
1. Gestionnaire crée une garantie
2. Partenaire demande une mise en jeu
3. Analyste consulte les données
4. Gestionnaire traite la demande

**Résultat attendu**: Workflow fluide, notifications appropriées

## Critères de validation

### Critères fonctionnels
- ✅ Toutes les fonctionnalités métier opérationnelles
- ✅ Validation des données correcte
- ✅ Processus transactionnels atomiques
- ✅ Audit complet des actions

### Critères techniques
- ✅ APIs RESTful conformes
- ✅ Interface responsive
- ✅ Performance acceptable
- ✅ Sécurité renforcée

### Critères utilisateur
- ✅ Interface intuitive
- ✅ Messages d'erreur clairs
- ✅ Feedback approprié
- ✅ Navigation fluide

## Outils de test recommandés

### Tests automatisés
- **Jest** pour les tests unitaires
- **Cypress** pour les tests E2E
- **Postman/Newman** pour les tests d'API
- **Lighthouse** pour les performances

### Tests manuels
- **Navigateurs multiples** (Chrome, Firefox, Safari, Edge)
- **Appareils mobiles** (responsive design)
- **Outils de développement** (DevTools)

## Rapport de test

### Template de rapport
```markdown
# Rapport de Test - [Date]

## Résumé exécutif
- Tests exécutés: X/Y
- Taux de réussite: Z%
- Bugs critiques: N
- Bugs mineurs: M

## Détail par module
[Pour chaque module testé]

## Bugs identifiés
[Liste des bugs avec priorité]

## Recommandations
[Actions correctives]
```

---

**Document créé le**: [Date]
**Version**: 1.0
**Auteur**: Équipe QA GesGar
**Prochaine révision**: [Date + 1 mois]
